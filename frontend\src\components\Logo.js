import React from 'react';

const Logo = ({ size = 32, color = '#000000' }) => {
  return (
    <div
      className="logo flex items-center justify-center"
      style={{
        width: size + 'px',
        height: size + 'px',
        flexShrink: 0 // 防止在flex容器中被压缩
      }}
    >
      <svg
        viewBox="0 0 100 100"
        xmlns="http://www.w3.org/2000/svg"
        style={{
          width: '100%',
          height: '100%',
          display: 'block' // 移除inline默认的底部空白
        }}
      >
        <circle cx="50" cy="50" r="50" fill={color} />
        <path d="M30 25 C35 27, 70 25, 75 25 C77 25, 75 30, 73 32 C70 35, 65 35, 65 35
                 L55 35 C53 35, 53 40, 53 42 C53 50, 55 65, 55 72 C55 75, 54 80, 50 80
                 C46 80, 45 77, 45 74 C45 68, 47 55, 47 42 C47 40, 47 35, 45 35
                 L35 35 C33 35, 28 35, 25 32 C23 30, 25 25, 30 25"
              fill="white" />
      </svg>
    </div>
  );
};

export default Logo; 