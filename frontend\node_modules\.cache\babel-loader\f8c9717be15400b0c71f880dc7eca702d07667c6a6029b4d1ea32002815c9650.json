{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\components\\\\Logo.js\";\nimport React from 'react';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst Logo = ({\n  size = 32,\n  color = '#000000'\n}) => {\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"logo flex items-center justify-center\",\n    style: {\n      width: size + 'px',\n      height: size + 'px',\n      flexShrink: 0 // 防止在flex容器中被压缩\n    },\n    children: /*#__PURE__*/_jsxDEV(\"svg\", {\n      viewBox: \"0 0 100 100\",\n      xmlns: \"http://www.w3.org/2000/svg\",\n      style: {\n        width: '100%',\n        height: '100%',\n        display: 'block' // 移除inline默认的底部空白\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"circle\", {\n        cx: \"50\",\n        cy: \"50\",\n        r: \"50\",\n        fill: color\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 22,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"path\", {\n        d: \"M30 25 C35 27, 70 25, 75 25 C77 25, 75 30, 73 32 C70 35, 65 35, 65 35\\r L55 35 C53 35, 53 40, 53 42 C53 50, 55 65, 55 72 C55 75, 54 80, 50 80\\r C46 80, 45 77, 45 74 C45 68, 47 55, 47 42 C47 40, 47 35, 45 35\\r L35 35 C33 35, 28 35, 25 32 C23 30, 25 25, 30 25\",\n        fill: \"white\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 23,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 13,\n      columnNumber: 7\n    }, this)\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 5,\n    columnNumber: 5\n  }, this);\n};\n_c = Logo;\nexport default Logo;\nvar _c;\n$RefreshReg$(_c, \"Logo\");", "map": {"version": 3, "names": ["React", "jsxDEV", "_jsxDEV", "Logo", "size", "color", "className", "style", "width", "height", "flexShrink", "children", "viewBox", "xmlns", "display", "cx", "cy", "r", "fill", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "d", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/components/Logo.js"], "sourcesContent": ["import React from 'react';\r\n\r\nconst Logo = ({ size = 32, color = '#000000' }) => {\r\n  return (\r\n    <div\r\n      className=\"logo flex items-center justify-center\"\r\n      style={{\r\n        width: size + 'px',\r\n        height: size + 'px',\r\n        flexShrink: 0 // 防止在flex容器中被压缩\r\n      }}\r\n    >\r\n      <svg\r\n        viewBox=\"0 0 100 100\"\r\n        xmlns=\"http://www.w3.org/2000/svg\"\r\n        style={{\r\n          width: '100%',\r\n          height: '100%',\r\n          display: 'block' // 移除inline默认的底部空白\r\n        }}\r\n      >\r\n        <circle cx=\"50\" cy=\"50\" r=\"50\" fill={color} />\r\n        <path d=\"M30 25 C35 27, 70 25, 75 25 C77 25, 75 30, 73 32 C70 35, 65 35, 65 35\r\n                 L55 35 C53 35, 53 40, 53 42 C53 50, 55 65, 55 72 C55 75, 54 80, 50 80\r\n                 C46 80, 45 77, 45 74 C45 68, 47 55, 47 42 C47 40, 47 35, 45 35\r\n                 L35 35 C33 35, 28 35, 25 32 C23 30, 25 25, 30 25\"\r\n              fill=\"white\" />\r\n      </svg>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Logo; "], "mappings": ";AAAA,OAAOA,KAAK,MAAM,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE1B,MAAMC,IAAI,GAAGA,CAAC;EAAEC,IAAI,GAAG,EAAE;EAAEC,KAAK,GAAG;AAAU,CAAC,KAAK;EACjD,oBACEH,OAAA;IACEI,SAAS,EAAC,uCAAuC;IACjDC,KAAK,EAAE;MACLC,KAAK,EAAEJ,IAAI,GAAG,IAAI;MAClBK,MAAM,EAAEL,IAAI,GAAG,IAAI;MACnBM,UAAU,EAAE,CAAC,CAAC;IAChB,CAAE;IAAAC,QAAA,eAEFT,OAAA;MACEU,OAAO,EAAC,aAAa;MACrBC,KAAK,EAAC,4BAA4B;MAClCN,KAAK,EAAE;QACLC,KAAK,EAAE,MAAM;QACbC,MAAM,EAAE,MAAM;QACdK,OAAO,EAAE,OAAO,CAAC;MACnB,CAAE;MAAAH,QAAA,gBAEFT,OAAA;QAAQa,EAAE,EAAC,IAAI;QAACC,EAAE,EAAC,IAAI;QAACC,CAAC,EAAC,IAAI;QAACC,IAAI,EAAEb;MAAM;QAAAc,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAC9CpB,OAAA;QAAMqB,CAAC,EAAC,mQAGkD;QACpDL,IAAI,EAAC;MAAO;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAClB;EAAC;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACE,EAAA,GA5BIrB,IAAI;AA8BV,eAAeA,IAAI;AAAC,IAAAqB,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}