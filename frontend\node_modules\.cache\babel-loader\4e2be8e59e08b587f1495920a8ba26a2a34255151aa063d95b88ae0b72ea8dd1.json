{"ast": null, "code": "var _jsxFileName = \"E:\\\\cursor\\\\ppt\\\\TikTodo-aippt\\\\frontend\\\\src\\\\views\\\\ChatView.js\",\n  _s = $RefreshSig$();\n// frontend/src/views/ChatView.js\nimport React, { useState, useRef, useEffect } from 'react';\nimport MessageInput from '../components/MessageInput';\nimport ChatMessage from '../components/ChatMessage';\nimport apiService from '../services/api';\nimport { FaPlus, FaComments, FaBolt } from 'react-icons/fa';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nconst ChatView = ({\n  currentChatId,\n  initialMessages = [],\n  onChatComplete\n}) => {\n  _s();\n  const [messages, setMessages] = useState([]);\n  const [isGenerating, setIsGenerating] = useState(false);\n  const [turboMode, setTurboMode] = useState(false); // 添加极速模式状态\n  const messagesEndRef = useRef(null);\n\n  // 使用initialMessages初始化聊天记录\n  useEffect(() => {\n    if (initialMessages && initialMessages.length > 0) {\n      setMessages(initialMessages);\n    }\n  }, [initialMessages]);\n\n  // 自动滚动到最新消息\n  useEffect(() => {\n    var _messagesEndRef$curre;\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n  }, [messages]);\n  const handleSendMessage = async messageData => {\n    // 兼容旧版本调用（直接传字符串）和新版本（传对象）\n    const messageText = typeof messageData === 'string' ? messageData : messageData.message;\n    const files = typeof messageData === 'object' ? messageData.files : [];\n    if (!(messageText !== null && messageText !== void 0 && messageText.trim()) && (!files || files.length === 0) || isGenerating) return;\n\n    // 添加用户消息\n    const userMessage = {\n      id: `user-${Date.now()}`,\n      sender: 'user',\n      text: messageText || '发送了文件',\n      files: files || [],\n      timestamp: new Date().toISOString()\n    };\n    setMessages(prev => [...prev, userMessage]);\n    setIsGenerating(true);\n    try {\n      // 开始流式AI响应\n      await apiService.streamChatResponse(messageText, progressData => {\n        setMessages(prevMessages => {\n          const newMessages = [...prevMessages];\n\n          // 处理系统消息 - 总是作为新的、独立的系统消息添加\n          if (progressData.sender === 'system') {\n            newMessages.push({\n              ...progressData,\n              type: 'system-info',\n              timestamp: progressData.timestamp || new Date().toISOString()\n            });\n            return newMessages;\n          }\n\n          // 处理AI消息 (sender === 'ai')\n          if (progressData.sender === 'ai') {\n            // 查找具有相同ID的现有AI消息\n            const aiMessageIndex = newMessages.findIndex(msg => msg.id === progressData.id);\n            if (aiMessageIndex !== -1) {\n              // 找到现有消息\n              const existingMessage = newMessages[aiMessageIndex];\n              if (progressData.is_append) {\n                // 如果是追加内容，将文本追加到现有消息\n                newMessages[aiMessageIndex] = {\n                  ...existingMessage,\n                  text: (existingMessage.text || '') + (progressData.text || ''),\n                  is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                  timestamp: progressData.timestamp || existingMessage.timestamp\n                };\n              } else {\n                // 如果不是追加，更新整个消息状态\n                newMessages[aiMessageIndex] = {\n                  ...existingMessage,\n                  text: progressData.text !== undefined ? progressData.text : existingMessage.text,\n                  is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                  stream_complete: progressData.stream_complete !== undefined ? progressData.stream_complete : existingMessage.stream_complete,\n                  timestamp: progressData.timestamp || existingMessage.timestamp\n                };\n              }\n            } else {\n              // 如果未找到现有AI消息，作为新消息添加\n              newMessages.push({\n                ...progressData,\n                type: 'ai',\n                timestamp: progressData.timestamp || new Date().toISOString()\n              });\n            }\n          }\n          return newMessages;\n        });\n      }, error => {\n        console.error('Chat streaming error:', error);\n        setIsGenerating(false);\n        setMessages(prev => [...prev, {\n          id: `error-${Date.now()}`,\n          sender: 'system',\n          type: 'ai_error',\n          text: `发生错误: ${error.message}`,\n          timestamp: new Date().toISOString()\n        }]);\n      }, () => {\n        // 完成回调\n        setIsGenerating(false);\n        // 通知父组件聊天完成，可能需要刷新聊天历史\n        if (onChatComplete) {\n          onChatComplete();\n        }\n      }, currentChatId,\n      // 传递当前聊天ID\n      turboMode // 传递极速模式状态\n      );\n    } catch (apiError) {\n      console.error('API call failed to start:', apiError);\n      setIsGenerating(false);\n      setMessages(prev => [...prev, {\n        id: `error-api-${Date.now()}`,\n        sender: 'system',\n        type: 'ai_error',\n        text: `无法连接到AI聊天服务: ${apiError.message}`,\n        timestamp: new Date().toISOString()\n      }]);\n    }\n  };\n  const handleNewChat = () => {\n    setMessages([]);\n    setIsGenerating(false);\n    // 导航到新的聊天页面，清除URL中的chatId参数\n    window.location.href = '/chat';\n  };\n\n  // 切换极速模式\n  const toggleTurboMode = () => {\n    setTurboMode(prev => !prev);\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"border-r border-gray-200 flex flex-col bg-gray-50 max-h-full flex-1\",\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\",\n      children: [/*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: handleNewChat,\n        className: \"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\",\n        title: \"\\u65B0\\u804A\\u5929\",\n        children: /*#__PURE__*/_jsxDEV(FaPlus, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 157,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 152,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex-1 text-base font-medium text-gray-800\",\n        children: /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"py-1 px-2 rounded\",\n          children: \"AI \\u804A\\u5929\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 160,\n          columnNumber: 21\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 159,\n        columnNumber: 17\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: toggleTurboMode,\n        className: `p-2 ml-2 rounded-full ${turboMode ? 'bg-yellow-500 text-white' : 'text-gray-500 hover:text-yellow-500 hover:bg-gray-100'}`,\n        title: turboMode ? \"关闭极速模式\" : \"开启极速模式\",\n        children: [/*#__PURE__*/_jsxDEV(FaBolt, {\n          size: 16\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 169,\n          columnNumber: 21\n        }, this), turboMode && /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"ml-1 text-xs\",\n          children: \"\\u6781\\u901F\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 170,\n          columnNumber: 35\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 162,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 151,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\",\n      style: {\n        height: \"calc(100% - 110px)\"\n      },\n      children: [messages.length === 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\",\n        children: [/*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-6xl mb-6 text-gray-300\",\n          children: \"\\uD83D\\uDCAC\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 178,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-2xl font-medium\",\n          children: \"\\u5F00\\u59CB\\u4F60\\u7684AI\\u5BF9\\u8BDD\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 179,\n          columnNumber: 25\n        }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n          className: \"text-sm text-gray-400 mt-2 text-center max-w-md\",\n          children: [\"\\u5728\\u8FD9\\u91CC\\u4E0EAI\\u81EA\\u7531\\u4EA4\\u6D41\\uFF0C\\u63D0\\u51FA\\u95EE\\u9898\\u6216\\u5BFB\\u6C42\\u5E2E\\u52A9\\u3002\", turboMode && /*#__PURE__*/_jsxDEV(\"span\", {\n            className: \"block mt-1 font-medium text-yellow-500\",\n            children: \"\\u5DF2\\u5F00\\u542F\\u6781\\u901F\\u6A21\\u5F0F\\uFF0C\\u54CD\\u5E94\\u66F4\\u5FEB\\u4F46\\u53EF\\u80FD\\u4E0D\\u4F7F\\u7528\\u641C\\u7D22\\u548C\\u6DF1\\u5EA6\\u601D\\u8003\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 182,\n            columnNumber: 43\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 25\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 21\n      }, this), messages.map((msg, index) => /*#__PURE__*/_jsxDEV(ChatMessage, {\n        message: msg\n      }, msg.id || index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 187,\n        columnNumber: 21\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: messagesEndRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 189,\n        columnNumber: 17\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 175,\n      columnNumber: 13\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: \"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\",\n      children: /*#__PURE__*/_jsxDEV(MessageInput, {\n        onSendMessage: handleSendMessage,\n        isGenerating: isGenerating,\n        placeholder: turboMode ? \"极速模式已开启，输入你的消息...\" : \"输入你的消息...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 194,\n        columnNumber: 17\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 193,\n      columnNumber: 13\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 149,\n    columnNumber: 9\n  }, this);\n};\n_s(ChatView, \"PskscMoThBEq+4UqggY83Zb/Uc0=\");\n_c = ChatView;\nexport default ChatView;\nvar _c;\n$RefreshReg$(_c, \"ChatView\");", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "MessageInput", "ChatMessage", "apiService", "FaPlus", "FaComments", "FaBolt", "jsxDEV", "_jsxDEV", "ChatView", "currentChatId", "initialMessages", "onChatComplete", "_s", "messages", "setMessages", "isGenerating", "setIsGenerating", "turboMode", "setTurboMode", "messagesEndRef", "length", "_messagesEndRef$curre", "current", "scrollIntoView", "behavior", "handleSendMessage", "messageData", "messageText", "message", "files", "trim", "userMessage", "id", "Date", "now", "sender", "text", "timestamp", "toISOString", "prev", "streamChatResponse", "progressData", "prevMessages", "newMessages", "push", "type", "aiMessageIndex", "findIndex", "msg", "existingMessage", "is_append", "is_streaming", "undefined", "stream_complete", "error", "console", "apiError", "handleNewChat", "window", "location", "href", "toggleTurboMode", "className", "children", "onClick", "title", "size", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "style", "height", "map", "index", "ref", "onSendMessage", "placeholder", "_c", "$RefreshReg$"], "sources": ["E:/cursor/ppt/TikTodo-aippt/frontend/src/views/ChatView.js"], "sourcesContent": ["// frontend/src/views/ChatView.js\nimport React, { useState, useRef, useEffect } from 'react';\nimport MessageInput from '../components/MessageInput';\nimport ChatMessage from '../components/ChatMessage';\nimport apiService from '../services/api';\nimport { FaPlus, FaComments, FaBolt } from 'react-icons/fa';\n\nconst ChatView = ({ currentChatId, initialMessages = [], onChatComplete }) => {\n    const [messages, setMessages] = useState([]);\n    const [isGenerating, setIsGenerating] = useState(false);\n    const [turboMode, setTurboMode] = useState(false); // 添加极速模式状态\n    const messagesEndRef = useRef(null);\n\n    // 使用initialMessages初始化聊天记录\n    useEffect(() => {\n        if (initialMessages && initialMessages.length > 0) {\n            setMessages(initialMessages);\n        }\n    }, [initialMessages]);\n\n    // 自动滚动到最新消息\n    useEffect(() => {\n        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    }, [messages]);\n\n    const handleSendMessage = async (messageData) => {\n        // 兼容旧版本调用（直接传字符串）和新版本（传对象）\n        const messageText = typeof messageData === 'string' ? messageData : messageData.message;\n        const files = typeof messageData === 'object' ? messageData.files : [];\n        \n        if ((!messageText?.trim() && (!files || files.length === 0)) || isGenerating) return;\n\n        // 添加用户消息\n        const userMessage = {\n            id: `user-${Date.now()}`,\n            sender: 'user',\n            text: messageText || '发送了文件',\n            files: files || [],\n            timestamp: new Date().toISOString()\n        };\n        setMessages(prev => [...prev, userMessage]);\n        setIsGenerating(true);\n\n        try {\n            // 开始流式AI响应\n            await apiService.streamChatResponse(\n                messageText,\n                (progressData) => {\n                    setMessages(prevMessages => {\n                        const newMessages = [...prevMessages];\n\n                        // 处理系统消息 - 总是作为新的、独立的系统消息添加\n                        if (progressData.sender === 'system') {\n                            newMessages.push({\n                                ...progressData,\n                                type: 'system-info',\n                                timestamp: progressData.timestamp || new Date().toISOString()\n                            });\n                            return newMessages;\n                        }\n\n                        // 处理AI消息 (sender === 'ai')\n                        if (progressData.sender === 'ai') {\n                            // 查找具有相同ID的现有AI消息\n                            const aiMessageIndex = newMessages.findIndex(msg => msg.id === progressData.id);\n\n                            if (aiMessageIndex !== -1) {\n                                // 找到现有消息\n                                const existingMessage = newMessages[aiMessageIndex];\n                                \n                                if (progressData.is_append) {\n                                    // 如果是追加内容，将文本追加到现有消息\n                                    newMessages[aiMessageIndex] = {\n                                        ...existingMessage,\n                                        text: (existingMessage.text || '') + (progressData.text || ''),\n                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                                        timestamp: progressData.timestamp || existingMessage.timestamp\n                                    };\n                                } else {\n                                    // 如果不是追加，更新整个消息状态\n                                    newMessages[aiMessageIndex] = {\n                                        ...existingMessage,\n                                        text: progressData.text !== undefined ? progressData.text : existingMessage.text,\n                                        is_streaming: progressData.is_streaming !== undefined ? progressData.is_streaming : existingMessage.is_streaming,\n                                        stream_complete: progressData.stream_complete !== undefined ? progressData.stream_complete : existingMessage.stream_complete,\n                                        timestamp: progressData.timestamp || existingMessage.timestamp\n                                    };\n                                }\n                            } else {\n                                // 如果未找到现有AI消息，作为新消息添加\n                                newMessages.push({\n                                    ...progressData,\n                                    type: 'ai',\n                                    timestamp: progressData.timestamp || new Date().toISOString()\n                                });\n                            }\n                        }\n                        return newMessages;\n                    });\n                },\n                (error) => {\n                    console.error('Chat streaming error:', error);\n                    setIsGenerating(false);\n                    setMessages(prev => [...prev, {\n                        id: `error-${Date.now()}`,\n                        sender: 'system',\n                        type: 'ai_error',\n                        text: `发生错误: ${error.message}`,\n                        timestamp: new Date().toISOString()\n                    }]);\n                },\n                () => {\n                    // 完成回调\n                    setIsGenerating(false);\n                    // 通知父组件聊天完成，可能需要刷新聊天历史\n                    if (onChatComplete) {\n                        onChatComplete();\n                    }\n                },\n                currentChatId, // 传递当前聊天ID\n                turboMode // 传递极速模式状态\n            );\n        } catch (apiError) {\n            console.error('API call failed to start:', apiError);\n            setIsGenerating(false);\n            setMessages(prev => [...prev, {\n                id: `error-api-${Date.now()}`,\n                sender: 'system',\n                type: 'ai_error',\n                text: `无法连接到AI聊天服务: ${apiError.message}`,\n                timestamp: new Date().toISOString()\n            }]);\n        }\n    };\n\n    const handleNewChat = () => {\n        setMessages([]);\n        setIsGenerating(false);\n        // 导航到新的聊天页面，清除URL中的chatId参数\n        window.location.href = '/chat';\n    };\n\n    // 切换极速模式\n    const toggleTurboMode = () => {\n        setTurboMode(prev => !prev);\n    };\n\n    return (\n        <div className=\"border-r border-gray-200 flex flex-col bg-gray-50 max-h-full flex-1\">\n            {/* Header for Chat View */}\n            <div className=\"h-16 px-3 border-b border-gray-200 bg-white flex items-center sticky top-0 z-10\">\n                <button \n                  onClick={handleNewChat}\n                  className=\"p-2 mr-2 text-gray-500 hover:text-tiktodo-blue rounded-full hover:bg-gray-100\"\n                  title=\"新聊天\"\n                >\n                    <FaPlus size={16} />\n                </button>\n                <div className=\"flex-1 text-base font-medium text-gray-800\">\n                    <span className=\"py-1 px-2 rounded\">AI 聊天</span>\n                </div>\n                <button \n                  onClick={toggleTurboMode}\n                  className={`p-2 ml-2 rounded-full ${turboMode \n                    ? 'bg-yellow-500 text-white' \n                    : 'text-gray-500 hover:text-yellow-500 hover:bg-gray-100'}`}\n                  title={turboMode ? \"关闭极速模式\" : \"开启极速模式\"}\n                >\n                    <FaBolt size={16} />\n                    {turboMode && <span className=\"ml-1 text-xs\">极速</span>}\n                </button>\n            </div>\n\n            {/* Chat Messages Area - Adjust to account for sticky header and footer */}\n            <div className=\"flex-1 overflow-y-auto p-4 space-y-5 custom-scrollbar\" style={{ height: \"calc(100% - 110px)\" }}>\n                {messages.length === 0 && (\n                    <div className=\"flex flex-col items-center justify-center h-full text-gray-400 p-8 min-h-[400px]\">\n                        <span className=\"text-6xl mb-6 text-gray-300\">💬</span>\n                        <span className=\"text-2xl font-medium\">开始你的AI对话</span>\n                        <p className=\"text-sm text-gray-400 mt-2 text-center max-w-md\">\n                            在这里与AI自由交流，提出问题或寻求帮助。\n                            {turboMode && <span className=\"block mt-1 font-medium text-yellow-500\">已开启极速模式，响应更快但可能不使用搜索和深度思考</span>}\n                        </p>\n                    </div>\n                )}\n                {messages.map((msg, index) => (\n                    <ChatMessage key={msg.id || index} message={msg} /> \n                ))}\n                <div ref={messagesEndRef} />\n            </div>\n\n            {/* Message Input Area */}\n            <div className=\"p-2 bg-gray-50 sticky bottom-0 border-t border-gray-200\">\n                <MessageInput \n                    onSendMessage={handleSendMessage} \n                    isGenerating={isGenerating}\n                    placeholder={turboMode ? \"极速模式已开启，输入你的消息...\" : \"输入你的消息...\"}\n                />\n            </div>\n        </div>\n    );\n};\n\nexport default ChatView;"], "mappings": ";;AAAA;AACA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAOC,YAAY,MAAM,4BAA4B;AACrD,OAAOC,WAAW,MAAM,2BAA2B;AACnD,OAAOC,UAAU,MAAM,iBAAiB;AACxC,SAASC,MAAM,EAAEC,UAAU,EAAEC,MAAM,QAAQ,gBAAgB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE5D,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,aAAa;EAAEC,eAAe,GAAG,EAAE;EAAEC;AAAe,CAAC,KAAK;EAAAC,EAAA;EAC1E,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGjB,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACkB,YAAY,EAAEC,eAAe,CAAC,GAAGnB,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAACoB,SAAS,EAAEC,YAAY,CAAC,GAAGrB,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC;EACnD,MAAMsB,cAAc,GAAGrB,MAAM,CAAC,IAAI,CAAC;;EAEnC;EACAC,SAAS,CAAC,MAAM;IACZ,IAAIW,eAAe,IAAIA,eAAe,CAACU,MAAM,GAAG,CAAC,EAAE;MAC/CN,WAAW,CAACJ,eAAe,CAAC;IAChC;EACJ,CAAC,EAAE,CAACA,eAAe,CAAC,CAAC;;EAErB;EACAX,SAAS,CAAC,MAAM;IAAA,IAAAsB,qBAAA;IACZ,CAAAA,qBAAA,GAAAF,cAAc,CAACG,OAAO,cAAAD,qBAAA,uBAAtBA,qBAAA,CAAwBE,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;EAClE,CAAC,EAAE,CAACX,QAAQ,CAAC,CAAC;EAEd,MAAMY,iBAAiB,GAAG,MAAOC,WAAW,IAAK;IAC7C;IACA,MAAMC,WAAW,GAAG,OAAOD,WAAW,KAAK,QAAQ,GAAGA,WAAW,GAAGA,WAAW,CAACE,OAAO;IACvF,MAAMC,KAAK,GAAG,OAAOH,WAAW,KAAK,QAAQ,GAAGA,WAAW,CAACG,KAAK,GAAG,EAAE;IAEtE,IAAK,EAACF,WAAW,aAAXA,WAAW,eAAXA,WAAW,CAAEG,IAAI,CAAC,CAAC,MAAK,CAACD,KAAK,IAAIA,KAAK,CAACT,MAAM,KAAK,CAAC,CAAC,IAAKL,YAAY,EAAE;;IAE9E;IACA,MAAMgB,WAAW,GAAG;MAChBC,EAAE,EAAE,QAAQC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxBC,MAAM,EAAE,MAAM;MACdC,IAAI,EAAET,WAAW,IAAI,OAAO;MAC5BE,KAAK,EAAEA,KAAK,IAAI,EAAE;MAClBQ,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;IACtC,CAAC;IACDxB,WAAW,CAACyB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAER,WAAW,CAAC,CAAC;IAC3Cf,eAAe,CAAC,IAAI,CAAC;IAErB,IAAI;MACA;MACA,MAAMd,UAAU,CAACsC,kBAAkB,CAC/Bb,WAAW,EACVc,YAAY,IAAK;QACd3B,WAAW,CAAC4B,YAAY,IAAI;UACxB,MAAMC,WAAW,GAAG,CAAC,GAAGD,YAAY,CAAC;;UAErC;UACA,IAAID,YAAY,CAACN,MAAM,KAAK,QAAQ,EAAE;YAClCQ,WAAW,CAACC,IAAI,CAAC;cACb,GAAGH,YAAY;cACfI,IAAI,EAAE,aAAa;cACnBR,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAI,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;YAChE,CAAC,CAAC;YACF,OAAOK,WAAW;UACtB;;UAEA;UACA,IAAIF,YAAY,CAACN,MAAM,KAAK,IAAI,EAAE;YAC9B;YACA,MAAMW,cAAc,GAAGH,WAAW,CAACI,SAAS,CAACC,GAAG,IAAIA,GAAG,CAAChB,EAAE,KAAKS,YAAY,CAACT,EAAE,CAAC;YAE/E,IAAIc,cAAc,KAAK,CAAC,CAAC,EAAE;cACvB;cACA,MAAMG,eAAe,GAAGN,WAAW,CAACG,cAAc,CAAC;cAEnD,IAAIL,YAAY,CAACS,SAAS,EAAE;gBACxB;gBACAP,WAAW,CAACG,cAAc,CAAC,GAAG;kBAC1B,GAAGG,eAAe;kBAClBb,IAAI,EAAE,CAACa,eAAe,CAACb,IAAI,IAAI,EAAE,KAAKK,YAAY,CAACL,IAAI,IAAI,EAAE,CAAC;kBAC9De,YAAY,EAAEV,YAAY,CAACU,YAAY,KAAKC,SAAS,GAAGX,YAAY,CAACU,YAAY,GAAGF,eAAe,CAACE,YAAY;kBAChHd,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAIY,eAAe,CAACZ;gBACzD,CAAC;cACL,CAAC,MAAM;gBACH;gBACAM,WAAW,CAACG,cAAc,CAAC,GAAG;kBAC1B,GAAGG,eAAe;kBAClBb,IAAI,EAAEK,YAAY,CAACL,IAAI,KAAKgB,SAAS,GAAGX,YAAY,CAACL,IAAI,GAAGa,eAAe,CAACb,IAAI;kBAChFe,YAAY,EAAEV,YAAY,CAACU,YAAY,KAAKC,SAAS,GAAGX,YAAY,CAACU,YAAY,GAAGF,eAAe,CAACE,YAAY;kBAChHE,eAAe,EAAEZ,YAAY,CAACY,eAAe,KAAKD,SAAS,GAAGX,YAAY,CAACY,eAAe,GAAGJ,eAAe,CAACI,eAAe;kBAC5HhB,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAIY,eAAe,CAACZ;gBACzD,CAAC;cACL;YACJ,CAAC,MAAM;cACH;cACAM,WAAW,CAACC,IAAI,CAAC;gBACb,GAAGH,YAAY;gBACfI,IAAI,EAAE,IAAI;gBACVR,SAAS,EAAEI,YAAY,CAACJ,SAAS,IAAI,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;cAChE,CAAC,CAAC;YACN;UACJ;UACA,OAAOK,WAAW;QACtB,CAAC,CAAC;MACN,CAAC,EACAW,KAAK,IAAK;QACPC,OAAO,CAACD,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QAC7CtC,eAAe,CAAC,KAAK,CAAC;QACtBF,WAAW,CAACyB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;UAC1BP,EAAE,EAAE,SAASC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;UACzBC,MAAM,EAAE,QAAQ;UAChBU,IAAI,EAAE,UAAU;UAChBT,IAAI,EAAE,SAASkB,KAAK,CAAC1B,OAAO,EAAE;UAC9BS,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;QACtC,CAAC,CAAC,CAAC;MACP,CAAC,EACD,MAAM;QACF;QACAtB,eAAe,CAAC,KAAK,CAAC;QACtB;QACA,IAAIL,cAAc,EAAE;UAChBA,cAAc,CAAC,CAAC;QACpB;MACJ,CAAC,EACDF,aAAa;MAAE;MACfQ,SAAS,CAAC;MACd,CAAC;IACL,CAAC,CAAC,OAAOuC,QAAQ,EAAE;MACfD,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEE,QAAQ,CAAC;MACpDxC,eAAe,CAAC,KAAK,CAAC;MACtBF,WAAW,CAACyB,IAAI,IAAI,CAAC,GAAGA,IAAI,EAAE;QAC1BP,EAAE,EAAE,aAAaC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;QAC7BC,MAAM,EAAE,QAAQ;QAChBU,IAAI,EAAE,UAAU;QAChBT,IAAI,EAAE,gBAAgBoB,QAAQ,CAAC5B,OAAO,EAAE;QACxCS,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC;MACtC,CAAC,CAAC,CAAC;IACP;EACJ,CAAC;EAED,MAAMmB,aAAa,GAAGA,CAAA,KAAM;IACxB3C,WAAW,CAAC,EAAE,CAAC;IACfE,eAAe,CAAC,KAAK,CAAC;IACtB;IACA0C,MAAM,CAACC,QAAQ,CAACC,IAAI,GAAG,OAAO;EAClC,CAAC;;EAED;EACA,MAAMC,eAAe,GAAGA,CAAA,KAAM;IAC1B3C,YAAY,CAACqB,IAAI,IAAI,CAACA,IAAI,CAAC;EAC/B,CAAC;EAED,oBACIhC,OAAA;IAAKuD,SAAS,EAAC,qEAAqE;IAAAC,QAAA,gBAEhFxD,OAAA;MAAKuD,SAAS,EAAC,iFAAiF;MAAAC,QAAA,gBAC5FxD,OAAA;QACEyD,OAAO,EAAEP,aAAc;QACvBK,SAAS,EAAC,+EAA+E;QACzFG,KAAK,EAAC,oBAAK;QAAAF,QAAA,eAETxD,OAAA,CAACJ,MAAM;UAAC+D,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAChB,CAAC,eACT/D,OAAA;QAAKuD,SAAS,EAAC,4CAA4C;QAAAC,QAAA,eACvDxD,OAAA;UAAMuD,SAAS,EAAC,mBAAmB;UAAAC,QAAA,EAAC;QAAK;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/C,CAAC,eACN/D,OAAA;QACEyD,OAAO,EAAEH,eAAgB;QACzBC,SAAS,EAAE,yBAAyB7C,SAAS,GACzC,0BAA0B,GAC1B,uDAAuD,EAAG;QAC9DgD,KAAK,EAAEhD,SAAS,GAAG,QAAQ,GAAG,QAAS;QAAA8C,QAAA,gBAErCxD,OAAA,CAACF,MAAM;UAAC6D,IAAI,EAAE;QAAG;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,EACnBrD,SAAS,iBAAIV,OAAA;UAAMuD,SAAS,EAAC,cAAc;UAAAC,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAClD,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC,eAGN/D,OAAA;MAAKuD,SAAS,EAAC,uDAAuD;MAACS,KAAK,EAAE;QAAEC,MAAM,EAAE;MAAqB,CAAE;MAAAT,QAAA,GAC1GlD,QAAQ,CAACO,MAAM,KAAK,CAAC,iBAClBb,OAAA;QAAKuD,SAAS,EAAC,kFAAkF;QAAAC,QAAA,gBAC7FxD,OAAA;UAAMuD,SAAS,EAAC,6BAA6B;UAAAC,QAAA,EAAC;QAAE;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACvD/D,OAAA;UAAMuD,SAAS,EAAC,sBAAsB;UAAAC,QAAA,EAAC;QAAQ;UAAAI,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAM,CAAC,eACtD/D,OAAA;UAAGuD,SAAS,EAAC,iDAAiD;UAAAC,QAAA,GAAC,sHAE3D,EAAC9C,SAAS,iBAAIV,OAAA;YAAMuD,SAAS,EAAC,wCAAwC;YAAAC,QAAA,EAAC;UAAyB;YAAAI,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACxG,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CACR,EACAzD,QAAQ,CAAC4D,GAAG,CAAC,CAACzB,GAAG,EAAE0B,KAAK,kBACrBnE,OAAA,CAACN,WAAW;QAAuB2B,OAAO,EAAEoB;MAAI,GAA9BA,GAAG,CAAChB,EAAE,IAAI0C,KAAK;QAAAP,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAiB,CACrD,CAAC,eACF/D,OAAA;QAAKoE,GAAG,EAAExD;MAAe;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC3B,CAAC,eAGN/D,OAAA;MAAKuD,SAAS,EAAC,yDAAyD;MAAAC,QAAA,eACpExD,OAAA,CAACP,YAAY;QACT4E,aAAa,EAAEnD,iBAAkB;QACjCV,YAAY,EAAEA,YAAa;QAC3B8D,WAAW,EAAE5D,SAAS,GAAG,mBAAmB,GAAG;MAAY;QAAAkD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC9D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACL,CAAC;AAEd,CAAC;AAAC1D,EAAA,CAlMIJ,QAAQ;AAAAsE,EAAA,GAARtE,QAAQ;AAoMd,eAAeA,QAAQ;AAAC,IAAAsE,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}